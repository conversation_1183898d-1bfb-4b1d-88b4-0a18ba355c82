package de.adesso.fischereiregister.core.ports;

import java.time.Instant;

/**
 * Service interface for managing mocked timestamps during test data import.
 * This service maintains a stack of mocked timestamps that can be used
 * instead of real timestamps during event handling when importing test data.
 */
public interface TimestampPort {

    /**
     * Pushes a mocked timestamp onto the stack.
     * 
     * @param timestamp the timestamp to push
     */
    void pushMockedTimestamp(Instant timestamp);

    /**
     * Pushes a mocked timestamp parsed from a string onto the stack.
     * Supports formats: "dd.MM.yyyy HH:mm:ss" and "dd.MM.yyyy" (defaults to 12:00:00)
     * 
     * @param timestampString the timestamp string to parse and push
     */
    void pushMockedTimestamp(String timestampString);

    /**
     * Pops the most recent mocked timestamp from the stack.
     * 
     * @return the popped timestamp, or null if stack is empty
     */
    Instant popMockedTimestamp();

    /**
     * Clears all mocked timestamps from the stack.
     */
    void clearMockedTimestamps();

    /**
     * Gets the appropriate timestamp to use in event handlers.
     * Returns the mocked timestamp if available, otherwise returns the provided real timestamp.
     * 
     * @param realTimestamp the real timestamp from @Timestamp annotation
     * @return the mocked timestamp if available, otherwise the real timestamp
     */
    Instant getEffectiveTimestamp(Instant realTimestamp);
}
