package de.adesso.fischereiregister.view.fees_statistics.eventhandling;

import de.adesso.fischereiregister.core.events.LicenseExtendedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseCreatedEvent;
import de.adesso.fischereiregister.core.events.RegularLicenseDigitizedEvent;
import de.adesso.fischereiregister.core.events.ReplacementCardOrderedEvent;
import de.adesso.fischereiregister.core.events.VacationLicenseCreatedEvent;
import de.adesso.fischereiregister.core.model.type.SubmissionType;
import de.adesso.fischereiregister.core.ports.TimestampPort;
import de.adesso.fischereiregister.view.fees_statistics.services.FeesStatisticsViewService;
import lombok.AllArgsConstructor;
import org.axonframework.eventhandling.EventHandler;
import org.axonframework.eventhandling.ResetHandler;
import org.axonframework.eventhandling.Timestamp;
import org.springframework.stereotype.Component;

import java.time.Instant;
import java.time.ZoneId;

@Component
@AllArgsConstructor
public class FeesStatisticsViewEventHandler {

    private final FeesStatisticsViewService feesStatisticsViewService;
    private final TimestampPort timestampPort;

    @ResetHandler
    public void onReset() {
        feesStatisticsViewService.deleteAll(); // clear projection table before replay
    }

    /**
     * Process fees from RegularLicenseCreatedEvent
     */
    @EventHandler
    void on(RegularLicenseCreatedEvent event, @Timestamp Instant timestamp) {
        Instant effectiveTimestamp = timestampPort.getEffectiveTimestamp(timestamp);
        // Process fees if they exist in the event
        if (event.fees() != null && !event.fees().isEmpty()) {
            event.fees().forEach(fee -> feesStatisticsViewService.updateOrCreateStatistic(
                    fee.getFederalState(),
                    event.issuedByOffice(),
                    event.submissionType(),
                    getTimestampYear(effectiveTimestamp),
                    fee.getPaymentInfo().getAmount()
            ));
        }
    }

    /**
     * Process fees from RegularLicenseDigitizedEvent
     */
    @EventHandler
    void on(RegularLicenseDigitizedEvent event, @Timestamp Instant timestamp) {
        Instant effectiveTimestamp = timestampPort.getEffectiveTimestamp(timestamp);
        // Process fees if they exist in the event
        if (event.fees() != null && !event.fees().isEmpty()) {
            event.fees().forEach(fee -> feesStatisticsViewService.updateOrCreateStatistic(
                    fee.getFederalState(),
                    event.issuedByOffice(),
                    SubmissionType.ANALOG,
                    getTimestampYear(effectiveTimestamp),
                    fee.getPaymentInfo().getAmount()
            ));
        }
    }

    /**
     * Process fees from VacationLicenseCreatedEvent
     */
    @EventHandler
    void on(VacationLicenseCreatedEvent event, @Timestamp Instant timestamp) {
        Instant effectiveTimestamp = timestampPort.getEffectiveTimestamp(timestamp);
        // Process fees if they exist in the event
        if (event.fees() != null && !event.fees().isEmpty()) {
            event.fees().forEach(fee -> feesStatisticsViewService.updateOrCreateStatistic(
                    fee.getFederalState(),
                    event.issuedByOffice(),
                    event.submissionType(),
                    getTimestampYear(effectiveTimestamp),
                    fee.getPaymentInfo().getAmount()
            ));
        }
    }

    /**
     * Process fees from LicenseExtendedEvent
     */
    @EventHandler
    void on(LicenseExtendedEvent event, @Timestamp Instant timestamp) {
        Instant effectiveTimestamp = timestampPort.getEffectiveTimestamp(timestamp);
        // Process fees if they exist in the event
        if (event.fees() != null && !event.fees().isEmpty()) {
            event.fees().forEach(fee -> feesStatisticsViewService.updateOrCreateStatistic(
                    fee.getFederalState(),
                    event.issuedByOffice(),
                    event.submissionType(),
                    getTimestampYear(effectiveTimestamp),
                    fee.getPaymentInfo().getAmount()
            ));
        }
    }

    /**
     * Process fees from ReplacementCardOrderedEvent
     */
    @EventHandler
    void on(ReplacementCardOrderedEvent event, @Timestamp Instant timestamp) {
        Instant effectiveTimestamp = timestampPort.getEffectiveTimestamp(timestamp);
        // Process fees if they exist in the event
        if (event.fees() != null && !event.fees().isEmpty()) {
            event.fees().forEach(fee -> feesStatisticsViewService.updateOrCreateStatistic(
                    fee.getFederalState(),
                    event.issuedByOffice(),
                    event.submissionType(),
                    getTimestampYear(effectiveTimestamp),
                    fee.getPaymentInfo().getAmount()
            ));
        }
    }

    private int getTimestampYear(Instant timestamp) {
        return timestamp.atZone(ZoneId.systemDefault()).getYear();
    }
}
