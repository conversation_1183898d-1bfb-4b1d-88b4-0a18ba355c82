package de.adesso.fischereiregister.registerservice.import_testdata;

import de.adesso.fischereiregister.core.ports.TimestampPort;
import org.springframework.stereotype.Service;

import java.time.Instant;
import java.time.LocalDateTime;
import java.time.ZoneId;
import java.time.format.DateTimeFormatter;
import java.time.format.DateTimeParseException;
import java.util.ArrayDeque;
import java.util.Deque;

@Service
public class MockedTimestampAdapter implements TimestampPort {

    private static final DateTimeFormatter GERMAN_DATE_TIME_FORMATTER = DateTimeFormatter.ofPattern("dd.MM.yyyy HH:mm:ss");

    private final ThreadLocal<Deque<Instant>> mockedTimestampStack = ThreadLocal.withInitial(ArrayDeque::new);

    @Override
    public void pushMockedTimestamp(Instant timestamp) {
        if (timestamp != null) {
            mockedTimestampStack.get().push(timestamp);
        }
    }

    @Override
    public void pushMockedTimestamp(String timestampString) {
        if (timestampString == null || timestampString.isBlank()) {
            return;
        }
        try {
            mockedTimestampStack.get().push(parseTimestamp(timestampString.trim()));
        } catch (DateTimeParseException ignored) {
            // Intentionally ignored for malformed inputs
        }
    }

    @Override
    public Instant popMockedTimestamp() {
        Deque<Instant> stack = mockedTimestampStack.get();
        return stack.isEmpty() ? null : stack.pop();
    }

    @Override
    public void clearMockedTimestamps() {
        mockedTimestampStack.get().clear();
    }

    @Override
    public Instant getEffectiveTimestamp(Instant realTimestamp) {
        Instant mocked = popMockedTimestamp();
        return mocked != null ? mocked : realTimestamp;
    }

    private Instant parseTimestamp(String input) {
        try {
            return LocalDateTime.parse(input, GERMAN_DATE_TIME_FORMATTER)
                    .atZone(ZoneId.systemDefault()).toInstant();
        } catch (DateTimeParseException e) {
            // Try with just the date (defaulting time to noon)
            LocalDateTime date = LocalDateTime.parse(input + " 12:00:00", GERMAN_DATE_TIME_FORMATTER);
            return date.atZone(ZoneId.systemDefault()).toInstant();
        }
    }
}
